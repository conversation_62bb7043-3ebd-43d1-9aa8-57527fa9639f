{"GlobalPropertiesHash": "QhN6lvdV/0xOwfUaKRGQo/IR2y8nZrqQUO1jT5Ca2Yo=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["yhBSD9Vr34P2+yiEoM+oYrjBmE53Jzzolfsrcq0lrBQ=", "JYJgM1zyx6joSWi752LPh/P0XbvbGtKq2Ue3SHlWWhk=", "myCe5qxGD/eLSCr3fWOxx/u/2z157nUGeGzaBjIdCKk=", "EmDZ1zCYnjuUBz0rnc3NP8IxMPKqtc7RlcVJ47O+1Ks=", "P8GT6U605yUTUWiW7ddZlw8Yk7s8quQ0gIJYHMMscvE=", "j8OE66nMIzkCO195vYVQJSXrw2VWe5K+fh5jX9rE2JU=", "lag4hTXpALBORZ0J9hPsc8n/NeRoPJAP9zUzsOpLwZA=", "7fGpbySNbRzn6QOJGRmbTq8+Jk3ChhrmW20E7BCTEQ4=", "FjvO8BUsJcLN7UXqz4GgYjHG+iCj9Pd2J2Ry4LMXuW8=", "P/okdnUyV+6pD74BsnTTpINH/DxpYvny7YePRfQNskY=", "Kvp4Fbl6PgkFJkEivgmSjOgBJ0hIEUulrXjDreMKm3w=", "Kg8rt4xc/+3kKQQ9IY9q1RtDcGFrib0fQ+CCSBWkVOE=", "tJuOmfjpqiU/HgYy2JQXhRMUaQY8cSrt4AoSIe3qlZ0=", "Z2uMrmHZ7cp3lMDT8a39gdQt5hODwRb8+lZO0kdCVcs=", "7R1MIdh+OTinAZgLkjY7Fpu4g3yt0q1rkKCa2+u3oos=", "g/j9jO8ruTjFhWBpJGCqfr31jzZHhsHnJ7Wj12brqyE=", "PCnP6qWxQ08BJHGxh9wRlLetEayNggJbFHFFTSMEICA=", "T7fN0rFaB7JlENY5+O5jfQE5dCIJpmReNClDKcPnpcY=", "PwkH0JX+HJE9dgsCK8TLbIqD6djGkRnuAvdkJmqVecc=", "XUmH+enax0Do5XlfeyA/wjwOgr8sp7BClA4MlfAjlyU=", "P2BBa5qi0PYzQlhc4YJb1RL4zLDzl35FurvjsLEOjLM=", "4wHy99H/6WWkWPKgAQD4QHp+pyVVC5E96YfnO+ILql8=", "gLE81P2evhCtx15QWVSpdTmWJTyQb0eFV+o6a8g0nwk=", "S7qZhDSTr/1YyOaDMXLHlaIIMsznS1whm6/gjgZh6iM=", "v9mXvsy24okl+fqSVxkV4auf4Jqr1593Hgb6zrAA0k0=", "9RrGQJD7Muxhpu1v7PsRlhUGLulg40mG74kpjHQmFI8=", "lw2fR91DTYx1+DLoiLtB9yQJ5BzJLjVq0OrgwFAkanU=", "2G00BHGP4bmg09lz8qieXmBCvA2Gu0sW/vC6pckv3uY=", "kKQLkyR+eVFEow2Q5+t4DJjIPdfJp+lMZv9OeA4CVAQ=", "/UlN+Ru3oGDBr7HBCrAgWjeRrPr94iGXdcBNSX/BSc0=", "mAUcqgPusC98XtG8kS3DgzH/Ns5RFC7x72UEztSyDTw=", "lLZd216kfuVbhjRh+Gebp7+D0ByXgVRAb8YJdBnOS+4=", "L2LyatzIyEX8Dkrqgrzp2dm20iy5mlUwGBEHdnznuVg=", "rAP3OoMNgN1FOLNXJD2Jy5Vw3ApzBfdVo1g6u1VVb1o=", "wW7NlPD9FcdGA21tJRTmPpmXUzOKONFqPJGJOPGrrvI=", "Zl7/mWYSqCMFYAdGtd8l6ZC9HE0ZIN4ccitUkGzhO9o=", "HRrQszrlavhwVw+YGTWWvApe3QLy8FjUX6p735+9Qjo=", "hRYl6SmOHqx5Gg+9LhlooCoBDsXLbYR0wWQ/hWeu9RQ=", "KsM0AvsOrzyvQmDERZN8E9YGaJU7SKfOG+YHKpFMYJc=", "h953SXw6rYtbL2K0UJBqmFa8Idqz7nYrWI75gdlMUm4=", "x/Okzr637VccxRcOOltY+KNVmP5yiuCQg3GAVK0UXdo=", "mtC/cJDJhMda1IL/FqNzbdDnQ/QgniG5s0AnR+LzjBA=", "Bd2noyLahR3j9VVYlDA+tq4tkFqqjyQRsHp+WnZo8+g=", "Q26z3f1jdDaHnUrqPSA0xTZ9W0ewbk6WncikMWluT7M=", "FqDCj1ipCNlMi0SW49oxdH8CSipcymOW4tM1YLnxmUY=", "LQli4JZImQkME4a7llBzTAlMCZG9e3VarcBClOMr6WY=", "2tIiM+AzqLiKVHRh1QoDH3uqzuTw3cqDGt7ijW2aHZY=", "DhW6Rn5+dpOOgCH/hHMJ2xmBYo+HGtQM/fFNw0i/FZw=", "4vrwsbyACqTbSba3A20VmT9vXAhpB576uGkAxqmPZwY=", "3dUPafX3pQv+eKyBytsOhjuCR6Diia4KdY2ePlNub4s=", "6iSuyfJQoEs9Hd/qfnAn2wyVZbziGpnBFq2Bs7OqXgo=", "DBMjIBPgFIwpLwEjC0Bmvd/sD77SBs1YpRivui29iWQ=", "L6PCk75vtDFwjintwbGfrsxba2xAi/KndpXeEmnZ+lQ=", "JVmdzLqar7FtFnH+PIhFBCxSpDJT5tNUqsBO1b5uYy8=", "1xVKwtYoe+f7jUc8riCcQuqqwn51p96Agn5+zGrH1Ps=", "EGU2x6jJhO0Q2aYwKFD1IJmVvFmbglCHP7+Pw+tau/I=", "Nq8H83tEXGdr4M6EhOxhsyDf05cezLq2+aJgVi44i6A=", "O6B8ANz+1LIwAIX4mxQjTH2mXnzBV7zXhozQkPt7zKc=", "iBdCZ2HINfMWueolf49LloSnKmGhC890Tq+IqlfPMfo=", "1oAHxP4sOab1tm8DdNJNRshexMgoOAEKLUlJFxippXo=", "Men2os7HZizIVyJ1BjCjX9IQcF0MXIAI1zeaceogbvU=", "VanmzchcZxbvLwVs5rM0RF3dpt1u9E5MzEZpxnJJ1BA=", "hXO0uGVvjp9Bm7EHLHRbVyGyeC+Heu2KPimnkb3InPc=", "6dABrh8kEWnwiLwb8b7L85cloqWCIk8D9o98wxzUiyw=", "1ED1s4wCskrjAUj7gUFK2SlvtrL5tr3wPSddRu9G9rE=", "aScfMaUd34Fu2ytHX5dRd1SnMLDIsqGrCckHwD+87bc=", "1rpUQ8pc/u7HIKmrSaw2gRYDaicv10n79uDcDqcycuc=", "OLFvKkVOR/5MQxhY00Y1RY0etlksVb8lk+2Q9sWYCgs=", "UuCd+Sgp0VQkB+P+dqHlmAiFsBWDRENIZCs7KxSIx7M="], "CachedAssets": {"yhBSD9Vr34P2+yiEoM+oYrjBmE53Jzzolfsrcq0lrBQ=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\css\\site.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-09-07T09:15:09.224735+00:00"}, "JYJgM1zyx6joSWi752LPh/P0XbvbGtKq2Ue3SHlWWhk=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\favicon.ico", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-09-07T09:15:09.6396255+00:00"}, "myCe5qxGD/eLSCr3fWOxx/u/2z157nUGeGzaBjIdCKk=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\js\\site.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-09-07T09:15:09.2257329+00:00"}, "EmDZ1zCYnjuUBz0rnc3NP8IxMPKqtc7RlcVJ47O+1Ks=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-09-07T09:15:09.4790544+00:00"}, "P8GT6U605yUTUWiW7ddZlw8Yk7s8quQ0gIJYHMMscvE=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-09-07T09:15:09.4820466+00:00"}, "j8OE66nMIzkCO195vYVQJSXrw2VWe5K+fh5jX9rE2JU=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-09-07T09:15:09.4830438+00:00"}, "lag4hTXpALBORZ0J9hPsc8n/NeRoPJAP9zUzsOpLwZA=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-09-07T09:15:09.4840414+00:00"}, "7fGpbySNbRzn6QOJGRmbTq8+Jk3ChhrmW20E7BCTEQ4=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-09-07T09:15:09.4850388+00:00"}, "FjvO8BUsJcLN7UXqz4GgYjHG+iCj9Pd2J2Ry4LMXuW8=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-09-07T09:15:09.4870333+00:00"}, "P/okdnUyV+6pD74BsnTTpINH/DxpYvny7YePRfQNskY=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-09-07T09:15:09.4880307+00:00"}, "Kvp4Fbl6PgkFJkEivgmSjOgBJ0hIEUulrXjDreMKm3w=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-09-07T09:15:09.5269253+00:00"}, "Kg8rt4xc/+3kKQQ9IY9q1RtDcGFrib0fQ+CCSBWkVOE=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-09-07T09:15:09.5279266+00:00"}, "tJuOmfjpqiU/HgYy2JQXhRMUaQY8cSrt4AoSIe3qlZ0=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-09-07T09:15:09.528922+00:00"}, "Z2uMrmHZ7cp3lMDT8a39gdQt5hODwRb8+lZO0kdCVcs=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-09-07T09:15:09.5299192+00:00"}, "7R1MIdh+OTinAZgLkjY7Fpu4g3yt0q1rkKCa2+u3oos=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-09-07T09:15:09.5309163+00:00"}, "g/j9jO8ruTjFhWBpJGCqfr31jzZHhsHnJ7Wj12brqyE=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-09-07T09:15:09.5319149+00:00"}, "PCnP6qWxQ08BJHGxh9wRlLetEayNggJbFHFFTSMEICA=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-09-07T09:15:09.532911+00:00"}, "T7fN0rFaB7JlENY5+O5jfQE5dCIJpmReNClDKcPnpcY=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-09-07T09:15:09.533908+00:00"}, "PwkH0JX+HJE9dgsCK8TLbIqD6djGkRnuAvdkJmqVecc=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-09-07T09:15:09.5349058+00:00"}, "XUmH+enax0Do5XlfeyA/wjwOgr8sp7BClA4MlfAjlyU=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-09-07T09:15:09.5369001+00:00"}, "P2BBa5qi0PYzQlhc4YJb1RL4zLDzl35FurvjsLEOjLM=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-09-07T09:15:09.5388948+00:00"}, "4wHy99H/6WWkWPKgAQD4QHp+pyVVC5E96YfnO+ILql8=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-09-07T09:15:09.5398916+00:00"}, "gLE81P2evhCtx15QWVSpdTmWJTyQb0eFV+o6a8g0nwk=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-09-07T09:15:09.5408897+00:00"}, "S7qZhDSTr/1YyOaDMXLHlaIIMsznS1whm6/gjgZh6iM=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-09-07T09:15:09.5418881+00:00"}, "v9mXvsy24okl+fqSVxkV4auf4Jqr1593Hgb6zrAA0k0=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-09-07T09:15:09.5438813+00:00"}, "9RrGQJD7Muxhpu1v7PsRlhUGLulg40mG74kpjHQmFI8=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-09-07T09:15:09.5448777+00:00"}, "lw2fR91DTYx1+DLoiLtB9yQJ5BzJLjVq0OrgwFAkanU=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-09-07T09:15:09.5468725+00:00"}, "2G00BHGP4bmg09lz8qieXmBCvA2Gu0sW/vC6pckv3uY=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-09-07T09:15:09.5488675+00:00"}, "kKQLkyR+eVFEow2Q5+t4DJjIPdfJp+lMZv9OeA4CVAQ=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-09-07T09:15:09.5538537+00:00"}, "/UlN+Ru3oGDBr7HBCrAgWjeRrPr94iGXdcBNSX/BSc0=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-09-07T09:15:09.5548531+00:00"}, "mAUcqgPusC98XtG8kS3DgzH/Ns5RFC7x72UEztSyDTw=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-09-07T09:15:09.5588409+00:00"}, "lLZd216kfuVbhjRh+Gebp7+D0ByXgVRAb8YJdBnOS+4=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-09-07T09:15:09.5598401+00:00"}, "L2LyatzIyEX8Dkrqgrzp2dm20iy5mlUwGBEHdnznuVg=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-09-07T09:15:09.5648243+00:00"}, "rAP3OoMNgN1FOLNXJD2Jy5Vw3ApzBfdVo1g6u1VVb1o=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-09-07T09:15:09.5668202+00:00"}, "wW7NlPD9FcdGA21tJRTmPpmXUzOKONFqPJGJOPGrrvI=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-09-07T09:15:09.57081+00:00"}, "Zl7/mWYSqCMFYAdGtd8l6ZC9HE0ZIN4ccitUkGzhO9o=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-09-07T09:15:09.5728057+00:00"}, "HRrQszrlavhwVw+YGTWWvApe3QLy8FjUX6p735+9Qjo=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-09-07T09:15:09.5767925+00:00"}, "hRYl6SmOHqx5Gg+9LhlooCoBDsXLbYR0wWQ/hWeu9RQ=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-09-07T09:15:09.5777917+00:00"}, "KsM0AvsOrzyvQmDERZN8E9YGaJU7SKfOG+YHKpFMYJc=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-09-07T09:15:09.5817801+00:00"}, "h953SXw6rYtbL2K0UJBqmFa8Idqz7nYrWI75gdlMUm4=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-09-07T09:15:09.5827779+00:00"}, "x/Okzr637VccxRcOOltY+KNVmP5yiuCQg3GAVK0UXdo=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-09-07T09:15:09.586767+00:00"}, "mtC/cJDJhMda1IL/FqNzbdDnQ/QgniG5s0AnR+LzjBA=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-09-07T09:15:09.5887615+00:00"}, "Bd2noyLahR3j9VVYlDA+tq4tkFqqjyQRsHp+WnZo8+g=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-09-07T09:15:09.5907561+00:00"}, "Q26z3f1jdDaHnUrqPSA0xTZ9W0ewbk6WncikMWluT7M=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-09-07T09:15:09.591755+00:00"}, "FqDCj1ipCNlMi0SW49oxdH8CSipcymOW4tM1YLnxmUY=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-09-07T09:15:09.5947445+00:00"}, "LQli4JZImQkME4a7llBzTAlMCZG9e3VarcBClOMr6WY=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-09-07T09:15:09.5967394+00:00"}, "2tIiM+AzqLiKVHRh1QoDH3uqzuTw3cqDGt7ijW2aHZY=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-09-07T09:15:09.5997315+00:00"}, "DhW6Rn5+dpOOgCH/hHMJ2xmBYo+HGtQM/fFNw0i/FZw=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-09-07T09:15:09.6356355+00:00"}, "4vrwsbyACqTbSba3A20VmT9vXAhpB576uGkAxqmPZwY=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-09-07T09:15:09.6416276+00:00"}, "3dUPafX3pQv+eKyBytsOhjuCR6Diia4KdY2ePlNub4s=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-09-07T09:15:09.642617+00:00"}, "6iSuyfJQoEs9Hd/qfnAn2wyVZbziGpnBFq2Bs7OqXgo=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-09-07T09:15:09.6436147+00:00"}, "DBMjIBPgFIwpLwEjC0Bmvd/sD77SBs1YpRivui29iWQ=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-09-07T09:15:09.2875666+00:00"}, "L6PCk75vtDFwjintwbGfrsxba2xAi/KndpXeEmnZ+lQ=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-09-07T09:15:09.2885638+00:00"}, "JVmdzLqar7FtFnH+PIhFBCxSpDJT5tNUqsBO1b5uYy8=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-09-07T09:15:09.2895613+00:00"}, "1xVKwtYoe+f7jUc8riCcQuqqwn51p96Agn5+zGrH1Ps=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-09-07T09:15:09.2905585+00:00"}, "EGU2x6jJhO0Q2aYwKFD1IJmVvFmbglCHP7+Pw+tau/I=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-09-07T09:15:09.6396255+00:00"}, "Nq8H83tEXGdr4M6EhOxhsyDf05cezLq2+aJgVi44i6A=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-09-07T09:15:09.2317161+00:00"}, "O6B8ANz+1LIwAIX4mxQjTH2mXnzBV7zXhozQkPt7zKc=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-09-07T09:15:09.2835768+00:00"}, "iBdCZ2HINfMWueolf49LloSnKmGhC890Tq+IqlfPMfo=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-09-07T09:15:09.2865694+00:00"}, "1oAHxP4sOab1tm8DdNJNRshexMgoOAEKLUlJFxippXo=": {"Identity": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "TestFramWorkWeb", "SourceType": "Discovered", "ContentRoot": "D:\\PROJECTS\\TestFramWorkWeb\\wwwroot\\", "BasePath": "_content/TestFramWorkWeb", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-09-07T09:15:09.6386278+00:00"}}, "CachedCopyCandidates": {}}