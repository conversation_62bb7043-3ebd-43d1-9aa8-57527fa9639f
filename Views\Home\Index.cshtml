﻿@model IEnumerable<TestCase>
@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">
    <h1 class="display-4">Welcome</h1>
    <p>Learn about <a href="https://learn.microsoft.com/aspnet/core">building Web apps with ASP.NET Core</a>.</p>
</div>


<table class="table">
    <thead>
        <tr>
            <th>Id</th>
            <th>Name</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var testCase in Model)
        {
            <tr>
                <td>@testCase.TestCaseCode</td>
                <td>@testCase.TestCaseName</td>
                <td>@testCase.Description</td>
            </tr>
        }
    </tbody>
</table>
