/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-pu7btw4pih] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-pu7btw4pih] {
  color: #0077cc;
}

.btn-primary[b-pu7btw4pih] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-pu7btw4pih], .nav-pills .show > .nav-link[b-pu7btw4pih] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-pu7btw4pih] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-pu7btw4pih] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-pu7btw4pih] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-pu7btw4pih] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-pu7btw4pih] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
