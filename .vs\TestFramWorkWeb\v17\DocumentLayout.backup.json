{"Version": 1, "WorkspaceRootPath": "D:\\PROJECTS\\TestFramWorkWeb\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\controllers\\homecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:controllers\\homecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\models\\testcase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:models\\testcase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\models\\testsuite.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:models\\testsuite.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\models\\module.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:models\\module.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\databaseaccess\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:databaseaccess\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|d:\\projects\\testframworkweb\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{23443061-C796-49A5-A50F-162C4194C5E9}|TestFramWorkWeb.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 17, "Children": [{"$type": "Bookmark", "Name": "ST:12:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{809f6ff3-8092-454a-8003-6d4091f9b5bb}"}, {"$type": "Bookmark", "Name": "ST:14:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:15:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:16:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:17:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:18:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:20:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{bdada759-5db7-402f-96e3-402b17c8c5b4}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Index.cshtml", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Views\\Home\\Index.cshtml", "RelativeDocumentMoniker": "Views\\Home\\Index.cshtml", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Views\\Home\\Index.cshtml*", "RelativeToolTip": "Views\\Home\\Index.cshtml*", "ViewState": "AgIAABEAAAAAAAAAAAAAAB0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-07T09:49:33.393Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "TestSuite.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\TestSuite.cs", "RelativeDocumentMoniker": "Models\\TestSuite.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\TestSuite.cs", "RelativeToolTip": "Models\\TestSuite.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T09:43:20.088Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "TestCase.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\TestCase.cs", "RelativeDocumentMoniker": "Models\\TestCase.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\TestCase.cs", "RelativeToolTip": "Models\\TestCase.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAgwAoAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T09:43:14.97Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Module.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\Module.cs", "RelativeDocumentMoniker": "Models\\Module.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Models\\Module.cs", "RelativeToolTip": "Models\\Module.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T09:43:08.166Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\DatabaseAccess\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "DatabaseAccess\\ApplicationDbContext.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\DatabaseAccess\\ApplicationDbContext.cs", "RelativeToolTip": "DatabaseAccess\\ApplicationDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T09:42:58.647Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "HomeController.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Controllers\\HomeController.cs", "RelativeDocumentMoniker": "Controllers\\HomeController.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Controllers\\HomeController.cs", "RelativeToolTip": "Controllers\\HomeController.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAQwBUAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T09:42:51.682Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\Program.cs*", "RelativeToolTip": "Program.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-07T09:40:48.834Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "appsettings.json", "DocumentMoniker": "D:\\PROJECTS\\TestFramWorkWeb\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "D:\\PROJECTS\\TestFramWorkWeb\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-07T09:40:07.884Z", "EditorCaption": ""}]}, {"DockedWidth": 1304, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}]}]}]}