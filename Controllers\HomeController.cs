using System.Diagnostics;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using TestFramWorkWeb.DatabaseAccess;
using TestFramWorkWeb.Models;

namespace TestFramWorkWeb.Controllers
{
    public class HomeController : Controller
    {
        private readonly ApplicationDbContext _context;

        public HomeController(ApplicationDbContext context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            var testCasesWapper = _context.TestCases.ToList();
            var testCases = testCasesWapper.Select(m => JsonSerializer.Deserialize<TestCase>(m.TestContent));
            return View(testCases);
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
